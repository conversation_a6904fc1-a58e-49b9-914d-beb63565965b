import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  Alert,
  Pressable,
} from "react-native";
import { router } from "expo-router";
import {
  UserCircle,
  PlusCircle,
  Star,
  Calendar,
  PlayCircle,
  Trash2,
  RefreshCw,
} from "lucide-react-native";
import { supabase } from "../../../utils/supabase";
import { Button } from "../../../components/ui/Button";
import { Card, CardContent } from "../../../components/ui/Card";

import { format } from "date-fns";

// Define the RunningProfile type
type RunningProfile = {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  runningGoal?: string;
  runningVideoSagittalUrl?: string;
  recommendation?: {
    shoeModel1?: { id: string; name: string };
    shoeModel2?: { id: string; name: string };
    shoeModel3?: { id: string; name: string };
    shoeModel4?: { id: string; name: string };
    shoeModel5?: { id: string; name: string };
    explanation1?: string;
  };
};

export default function RunningProfilesScreen() {
  const [profiles, setProfiles] = useState<RunningProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get("window").width;

  useEffect(() => {
    fetchProfiles();
  }, []);

  const fetchProfiles = async () => {
    setLoading(true);
    setError(null);
    try {
      // Get current user first
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      console.log("Auth user:", user);
      console.log("Auth error:", userError);

      if (userError || !user) {
        throw new Error("User not authenticated");
      }

      // For now, use mock data since direct Supabase queries are blocked by RLS
      // TODO: Set up API server or fix RLS policies
      console.log("Using mock data for user:", user.id);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockProfiles: RunningProfile[] = [
        {
          id: "profile-1",
          name: "Morning Run Profile",
          createdAt: "2024-05-20T08:00:00Z",
          updatedAt: "2024-05-20T08:00:00Z",
          isDefault: true,
          runningGoal: "Endurance Training",
          runningVideoSagittalUrl: "https://example.com/video1.mp4",
          recommendation: {
            shoeModel1: { id: "1", name: "Nike Air Zoom Pegasus" },
            shoeModel2: { id: "2", name: "Adidas Ultraboost" },
            shoeModel3: { id: "3", name: "Brooks Ghost" },
            shoeModel4: { id: "4", name: "Asics Gel-Nimbus" },
            shoeModel5: { id: "5", name: "New Balance Fresh Foam" },
            explanation1: "Great for daily training with excellent cushioning",
          },
        },
        {
          id: "profile-2",
          name: "Speed Training",
          createdAt: "2024-05-19T10:00:00Z",
          updatedAt: "2024-05-19T10:00:00Z",
          isDefault: false,
          runningGoal: "Speed Work",
          runningVideoSagittalUrl: undefined,
          recommendation: {
            shoeModel1: { id: "6", name: "Nike Vaporfly" },
            shoeModel2: { id: "7", name: "Adidas Adizero" },
            shoeModel3: { id: "8", name: "Saucony Endorphin" },
            shoeModel4: { id: "9", name: "Hoka Carbon X" },
            shoeModel5: { id: "10", name: "Brooks Hyperion" },
            explanation1: "Lightweight and responsive for speed training",
          },
        },
      ];

      setProfiles(mockProfiles);
    } catch (err) {
      console.error("Error fetching profiles:", err);
      setError(
        err instanceof Error ? err.message : "An unexpected error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleProfilePress = (profile: RunningProfile) => {
    console.log("Profile pressed:", profile.id, profile.name);
    router.push(`/running-profiles/${profile.id}`);
  };

  const handleDeleteProfile = async (id: string) => {
    Alert.alert(
      "Delete Profile",
      "Are you sure you want to delete this running profile? This action cannot be undone.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              setDeletingId(id);

              // Simulate API delay
              await new Promise((resolve) => setTimeout(resolve, 500));

              // TODO: Replace with actual API call
              console.log("Mock: Deleting profile", id);

              // Remove from local state
              setProfiles(profiles.filter((profile) => profile.id !== id));
            } catch (err) {
              console.error("Error deleting profile:", err);
              setError(
                err instanceof Error ? err.message : "Failed to delete profile"
              );
            } finally {
              setDeletingId(null);
            }
          },
        },
      ]
    );
  };

  const handleSetDefault = async (id: string) => {
    try {
      setSettingDefaultId(id);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // TODO: Replace with actual API call
      console.log("Mock: Setting default profile", id);

      // Update local state
      setProfiles(
        profiles.map((profile) => ({
          ...profile,
          isDefault: profile.id === id,
        }))
      );
    } catch (err) {
      console.error("Error setting default profile:", err);
      setError(
        err instanceof Error ? err.message : "Failed to set default profile"
      );
    } finally {
      setSettingDefaultId(null);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchProfiles();
    setRefreshing(false);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd.MM.yyyy");
  };

  const renderProfileCard = ({
    item,
    index,
  }: {
    item: RunningProfile | null;
    index: number;
  }) => {
    // If item is null, render the "Create New Profile" card
    if (item === null) {
      return (
        <View style={{ width: screenWidth * 0.75 }}>
          <Card
            className="h-[550px] bg-primary rounded-2xl overflow-hidden shadow-lg"
            onPress={() => router.push("/running-profile/new")}
          >
            <View className="flex-1 p-5">
              <Text className="text-xl font-semibold text-primary-foreground mb-1">
                Create New Profile
              </Text>
              <Text className="text-sm text-primary-foreground/80">
                Add another runner or variation
              </Text>

              <View className="flex-1 items-center justify-center">
                <View className="w-20 h-20 rounded-full bg-primary-foreground/20 items-center justify-center">
                  <PlusCircle size={40} color="#f7f6f4" />
                </View>
              </View>
            </View>
          </Card>
        </View>
      );
    }

    // Regular profile card
    const profile = item;
    const isDefault = profile.isDefault;
    const showVideo =
      isDefault || (index === 0 && !profiles.some((p) => p.isDefault));

    // Get recommendations (up to 5)
    const recommendations = [];
    if (profile.recommendation?.shoeModel1) {
      recommendations.push({
        name: profile.recommendation.shoeModel1.name,
        id: profile.recommendation.shoeModel1.id,
      });
    }
    if (profile.recommendation?.shoeModel2) {
      recommendations.push({
        name: profile.recommendation.shoeModel2.name,
        id: profile.recommendation.shoeModel2.id,
      });
    }
    if (profile.recommendation?.shoeModel3) {
      recommendations.push({
        name: profile.recommendation.shoeModel3.name,
        id: profile.recommendation.shoeModel3.id,
      });
    }
    if (profile.recommendation?.shoeModel4) {
      recommendations.push({
        name: profile.recommendation.shoeModel4.name,
        id: profile.recommendation.shoeModel4.id,
      });
    }
    if (profile.recommendation?.shoeModel5) {
      recommendations.push({
        name: profile.recommendation.shoeModel5.name,
        id: profile.recommendation.shoeModel5.id,
      });
    }

    return (
      <View style={{ width: screenWidth * 0.75 }}>
        <Pressable
          onPress={() => handleProfilePress(profile)}
          className="h-[550px] bg-black/20 rounded-2xl overflow-hidden shadow-lg bg-card border border-border"
        >
          {/* Background - Solid Green */}
          <View className="absolute inset-0 w-full h-full">
            <View className="flex-1 bg-green-500">
              <View className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30" />
            </View>
          </View>

          {/* Content Overlay */}
          <View className="flex-1 z-10">
            {/* Header */}
            <View className="p-5 flex-row justify-between items-start">
              <View className="flex-1">
                <Text className="text-xl font-semibold text-white mb-1">
                  {profile.name}
                </Text>
                <View className="flex-row items-center">
                  <Calendar size={12} color="#fff" opacity={0.8} />
                  <Text className="ml-1 text-xs text-white/80">
                    {formatDate(profile.createdAt)}
                  </Text>
                  {isDefault && (
                    <View className="ml-2 flex-row items-center bg-white/20 px-2 py-0.5 rounded-full">
                      <Star size={12} color="#fff" />
                      <Text className="ml-1 text-xs text-white">Default</Text>
                    </View>
                  )}
                </View>
              </View>

              {/* Action Menu */}
              <View className="flex-row gap-2">
                {!isDefault && (
                  <TouchableOpacity
                    onPress={(e) => {
                      e.stopPropagation();
                      handleSetDefault(profile.id);
                    }}
                    disabled={settingDefaultId === profile.id}
                    className="w-8 h-8 rounded-full bg-white/20 items-center justify-center"
                  >
                    {settingDefaultId === profile.id ? (
                      <ActivityIndicator size="small" color="#fff" />
                    ) : (
                      <Star size={16} color="#fff" />
                    )}
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation();
                    handleDeleteProfile(profile.id);
                  }}
                  disabled={deletingId === profile.id}
                  className="w-8 h-8 rounded-full bg-red-500/20 items-center justify-center"
                >
                  {deletingId === profile.id ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <Trash2 size={16} color="#fff" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {/* Main Content Area */}
            <View className="flex-1 p-5 pt-0 pb-2">
              {/* Profile Info */}
              {profile.runningGoal && (
                <View className="mb-3">
                  <Text className="text-sm text-white/90">
                    <Text className="font-medium">Goal:</Text>{" "}
                    {profile.runningGoal}
                  </Text>
                </View>
              )}

              {/* Video Play Button (centered) */}
              {showVideo && profile.runningVideoSagittalUrl && (
                <View className="flex-1 items-center justify-center">
                  <View className="w-16 h-16 rounded-full bg-white/20 items-center justify-center">
                    <PlayCircle size={32} color="#fff" />
                  </View>
                </View>
              )}

              {/* If no video, add spacer */}
              {(!showVideo || !profile.runningVideoSagittalUrl) && (
                <View className="flex-1" />
              )}

              {/* Recommendations Section */}
              {recommendations.length > 0 && (
                <View className="mt-auto mb-0">
                  <Text className="text-sm font-medium text-white/90 mb-2">
                    Top Recommendations
                  </Text>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    className="pb-1"
                  >
                    <View className="flex-row gap-2">
                      {recommendations.map((rec, idx) => (
                        <TouchableOpacity
                          key={rec.id}
                          className="px-4 py-3 rounded-lg bg-black/40 border border-white/10"
                          onPress={(e) => {
                            e.stopPropagation();
                            router.push(`/shoes/${rec.id}`);
                          }}
                        >
                          <View className="flex-row items-center">
                            <View className="w-6 h-6 rounded-full bg-white/10 items-center justify-center mr-2">
                              <Text className="text-xs text-white/80">
                                {idx + 1}
                              </Text>
                            </View>
                            <Text className="text-xs text-white/90">
                              {rec.name}
                            </Text>
                          </View>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </ScrollView>
                </View>
              )}
            </View>
          </View>
        </Pressable>
      </View>
    );
  };

  if (loading) {
    return (
      <View className="flex-1 bg-background" style={{ paddingBottom: 80 }}>
        <View className="px-4 pt-4 pb-2">
          <Text className="text-2xl font-semibold text-foreground">
            Running Profiles
          </Text>
        </View>
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#23453b" />
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 bg-background" style={{ paddingBottom: 80 }}>
        <View className="px-4 pt-4 pb-2">
          <Text className="text-2xl font-semibold text-foreground">
            Running Profiles
          </Text>
        </View>
        <View className="flex-1 items-center justify-center px-4">
          <Card className="bg-red-100 border border-red-500/50 w-full max-w-md">
            <CardContent className="p-3">
              <Text className="text-red-800 text-xs">{error}</Text>
            </CardContent>
          </Card>
        </View>
      </View>
    );
  }

  if (profiles.length === 0) {
    return (
      <View className="flex-1 bg-background" style={{ paddingBottom: 80 }}>
        <View className="px-4 pt-4 pb-2">
          <Text className="text-2xl font-semibold text-foreground">
            Running Profiles
          </Text>
        </View>
        <View className="flex-1 items-center justify-center px-4">
          <View className="border-2 border-dashed border-zinc-300 rounded-lg p-8 items-center w-full max-w-md">
            <UserCircle size={40} color="#a1a1aa" />
            <Text className="text-lg font-medium text-zinc-800 mt-4 mb-2">
              No running profiles yet
            </Text>
            <Text className="text-sm text-zinc-500 mb-4 text-center">
              Create your first profile to unlock personalized shoe matches.
            </Text>
            <Button
              onPress={() => router.push("/running-profile/new")}
              className="bg-brand-green"
            >
              <View className="flex-row items-center">
                <PlusCircle size={16} color="#fff" />
                <Text className="ml-1.5 text-white">Create Profile</Text>
              </View>
            </Button>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background" style={{ paddingBottom: 80 }}>
      {/* Page Title */}
      <View className="px-4 pt-4 pb-2">
        <View className="flex-row items-center justify-between">
          <Text className="text-2xl font-semibold text-foreground">
            Running Profiles
          </Text>
          <View className="flex-row gap-2">
            <TouchableOpacity
              onPress={handleRefresh}
              disabled={refreshing}
              className="w-10 h-10 rounded-full bg-secondary items-center justify-center"
            >
              {refreshing ? (
                <ActivityIndicator size="small" color="#374151" />
              ) : (
                <RefreshCw size={16} color="#374151" />
              )}
            </TouchableOpacity>
            <Button
              onPress={() => router.push("/running-profile/new")}
              className="bg-primary"
            >
              <View className="flex-row items-center">
                <PlusCircle size={16} color="#fff" />
                <Text className="ml-2 text-white">New Profile</Text>
              </View>
            </Button>
          </View>
        </View>
      </View>

      {/* Profiles List - Tinder Style */}
      <View className="flex-1 justify-center">
        <FlatList
          ref={flatListRef}
          data={[...profiles, null]} // Add null for the "Create New Profile" card
          renderItem={renderProfileCard}
          keyExtractor={(item) => (item ? item.id : "new-profile")}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          snapToInterval={screenWidth * 0.8} // Card width with peek effect
          decelerationRate="fast"
          contentContainerStyle={{
            paddingHorizontal: screenWidth * 0.125, // Center the first card
            alignItems: "center",
          }}
          ItemSeparatorComponent={() => (
            <View style={{ width: screenWidth * 0.05 }} />
          )}
        />
      </View>
    </View>
  );
}
