import { createBrowserClient } from "@supabase/ssr";

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        // Configure auth settings for better token handling
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        // Add retry logic for token refresh
        retryAttempts: 3,
      },
      // Add global error handling
      global: {
        headers: {
          "X-Client-Info": "eigen-web-app",
        },
      },
    }
  );
}
