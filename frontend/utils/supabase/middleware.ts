import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async getAll() {
          return request.cookies.getAll();
        },
        async setAll(cookiesToSet) {
          for (const { name, value, options } of cookiesToSet) {
            request.cookies.set(name, value);
          }
          supabaseResponse = NextResponse.next({
            request,
          });
          for (const { name, value, options } of cookiesToSet) {
            supabaseResponse.cookies.set(name, value, options);
          }
        },
      },
    }
  );

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  let user = null;
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      // Log refresh token errors but don't throw - let the app handle gracefully
      if (
        error.message?.includes("refresh_token_not_found") ||
        error.message?.includes("Invalid Refresh Token")
      ) {
        console.warn("Refresh token error in middleware:", error.message);
        // Clear potentially corrupted session cookies with proper options
        const cookieOptions = {
          path: "/",
          domain: undefined, // Let browser determine
          maxAge: 0,
          httpOnly: false,
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax" as const,
        };

        // Clear all possible Supabase auth cookies
        const authCookieNames = [
          "sb-access-token",
          "sb-refresh-token",
          "supabase-auth-token",
          "supabase.auth.token",
        ];

        authCookieNames.forEach((cookieName) => {
          supabaseResponse.cookies.set(cookieName, "", cookieOptions);
        });
      } else {
        console.error("Auth error in middleware:", error);
      }
    } else {
      user = data.user;
    }
  } catch (error) {
    console.error("Unexpected error in middleware auth check:", error);
  }

  // If no session and trying to access protected routes, redirect to login
  const isAuthRoute = request.nextUrl.pathname.startsWith("/(auth)");
  const isProtectedRoute =
    request.nextUrl.pathname.startsWith("/profile") ||
    request.nextUrl.pathname.startsWith("/running-profiles") ||
    request.nextUrl.pathname.startsWith("/recommendations") ||
    request.nextUrl.pathname.startsWith("/orders") ||
    request.nextUrl.pathname.startsWith("/customize") ||
    request.nextUrl.pathname.startsWith("/create-eigen-profile");

  if (!user && isProtectedRoute) {
    console.log(
      `Redirecting unauthenticated user from ${request.nextUrl.pathname} to signin`
    );
    const redirectUrl = new URL("/signin", request.url);
    return NextResponse.redirect(redirectUrl);
  }

  // If session exists and trying to access auth routes, redirect to running-profiles
  if (user && isAuthRoute) {
    console.log(
      `Redirecting authenticated user from ${request.nextUrl.pathname} to running-profiles`
    );
    const redirectUrl = new URL("/running-profiles", request.url);
    return NextResponse.redirect(redirectUrl);
  }

  return supabaseResponse;
}
