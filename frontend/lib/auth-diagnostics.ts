/**
 * Authentication diagnostics and health check utilities
 * 
 * This module provides tools to diagnose authentication issues
 * and verify Supabase configuration.
 */

import { createClient } from "@/utils/supabase/client";
import { logAuthError } from "./auth-error-handler";

export interface AuthDiagnostics {
  supabaseConfigValid: boolean;
  clientInitialized: boolean;
  sessionExists: boolean;
  userExists: boolean;
  tokenValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

/**
 * Runs comprehensive authentication diagnostics
 */
export async function runAuthDiagnostics(): Promise<AuthDiagnostics> {
  const diagnostics: AuthDiagnostics = {
    supabaseConfigValid: false,
    clientInitialized: false,
    sessionExists: false,
    userExists: false,
    tokenValid: false,
    errors: [],
    warnings: [],
    recommendations: [],
  };

  try {
    // Check environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      diagnostics.errors.push("Missing Supabase environment variables");
      diagnostics.recommendations.push("Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set");
      return diagnostics;
    }

    if (supabaseUrl.includes("your-project") || supabaseAnonKey.includes("your-anon-key")) {
      diagnostics.errors.push("Supabase environment variables contain placeholder values");
      diagnostics.recommendations.push("Replace placeholder values with actual Supabase project credentials");
      return diagnostics;
    }

    diagnostics.supabaseConfigValid = true;

    // Try to initialize client
    const supabase = createClient();
    diagnostics.clientInitialized = true;

    // Check session
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        logAuthError(sessionError, "auth-diagnostics-session");
        diagnostics.errors.push(`Session error: ${sessionError.message}`);
        
        if (sessionError.message.includes('refresh_token_not_found')) {
          diagnostics.warnings.push("Refresh token not found - user may need to re-authenticate");
          diagnostics.recommendations.push("Clear browser storage and sign in again");
        }
      } else if (sessionData.session) {
        diagnostics.sessionExists = true;
        
        // Check if token is expired
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = sessionData.session.expires_at;
        
        if (expiresAt && expiresAt < now) {
          diagnostics.warnings.push("Session token has expired");
          diagnostics.recommendations.push("Token refresh should happen automatically");
        } else {
          diagnostics.tokenValid = true;
        }
      }
    } catch (error) {
      logAuthError(error, "auth-diagnostics-session-unexpected");
      diagnostics.errors.push(`Unexpected session error: ${error}`);
    }

    // Check user
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        logAuthError(userError, "auth-diagnostics-user");
        diagnostics.errors.push(`User error: ${userError.message}`);
      } else if (userData.user) {
        diagnostics.userExists = true;
      }
    } catch (error) {
      logAuthError(error, "auth-diagnostics-user-unexpected");
      diagnostics.errors.push(`Unexpected user error: ${error}`);
    }

    // Add general recommendations
    if (!diagnostics.sessionExists && !diagnostics.userExists) {
      diagnostics.recommendations.push("User is not authenticated - redirect to sign in");
    }

    if (diagnostics.sessionExists && !diagnostics.userExists) {
      diagnostics.warnings.push("Session exists but user data is missing");
      diagnostics.recommendations.push("This may indicate a session/user data mismatch");
    }

    if (diagnostics.errors.length === 0 && diagnostics.warnings.length === 0) {
      diagnostics.recommendations.push("Authentication appears to be working correctly");
    }

  } catch (error) {
    logAuthError(error, "auth-diagnostics-general");
    diagnostics.errors.push(`General diagnostics error: ${error}`);
  }

  return diagnostics;
}

/**
 * Logs authentication diagnostics in a readable format
 */
export function logAuthDiagnostics(diagnostics: AuthDiagnostics): void {
  console.group("🔍 Authentication Diagnostics");
  
  console.log("📊 Status:");
  console.log(`  ✅ Supabase Config Valid: ${diagnostics.supabaseConfigValid}`);
  console.log(`  ✅ Client Initialized: ${diagnostics.clientInitialized}`);
  console.log(`  ✅ Session Exists: ${diagnostics.sessionExists}`);
  console.log(`  ✅ User Exists: ${diagnostics.userExists}`);
  console.log(`  ✅ Token Valid: ${diagnostics.tokenValid}`);

  if (diagnostics.errors.length > 0) {
    console.log("\n❌ Errors:");
    diagnostics.errors.forEach(error => console.log(`  • ${error}`));
  }

  if (diagnostics.warnings.length > 0) {
    console.log("\n⚠️ Warnings:");
    diagnostics.warnings.forEach(warning => console.log(`  • ${warning}`));
  }

  if (diagnostics.recommendations.length > 0) {
    console.log("\n💡 Recommendations:");
    diagnostics.recommendations.forEach(rec => console.log(`  • ${rec}`));
  }

  console.groupEnd();
}

/**
 * Runs diagnostics and logs results (useful for debugging)
 */
export async function debugAuth(): Promise<void> {
  const diagnostics = await runAuthDiagnostics();
  logAuthDiagnostics(diagnostics);
}

/**
 * Checks if the current authentication state is healthy
 */
export async function isAuthHealthy(): Promise<boolean> {
  const diagnostics = await runAuthDiagnostics();
  return diagnostics.errors.length === 0 && 
         diagnostics.supabaseConfigValid && 
         diagnostics.clientInitialized;
}

/**
 * Gets a summary of authentication status for display
 */
export async function getAuthStatusSummary(): Promise<{
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details?: string[];
}> {
  const diagnostics = await runAuthDiagnostics();

  if (diagnostics.errors.length > 0) {
    return {
      status: 'error',
      message: 'Authentication errors detected',
      details: diagnostics.errors,
    };
  }

  if (diagnostics.warnings.length > 0) {
    return {
      status: 'warning',
      message: 'Authentication warnings detected',
      details: diagnostics.warnings,
    };
  }

  if (diagnostics.sessionExists && diagnostics.userExists && diagnostics.tokenValid) {
    return {
      status: 'healthy',
      message: 'User is authenticated and session is valid',
    };
  }

  if (!diagnostics.sessionExists && !diagnostics.userExists) {
    return {
      status: 'healthy',
      message: 'User is not authenticated (expected for public pages)',
    };
  }

  return {
    status: 'warning',
    message: 'Authentication state is unclear',
    details: diagnostics.recommendations,
  };
}

/**
 * Clears all authentication data and resets state
 */
export async function resetAuthState(): Promise<void> {
  try {
    const supabase = createClient();
    
    // Sign out from Supabase
    await supabase.auth.signOut();
    
    // Clear local storage
    const authKeys = [
      'supabase.auth.token',
      'sb-access-token',
      'sb-refresh-token',
    ];
    
    authKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      } catch (error) {
        console.warn(`Error clearing ${key}:`, error);
      }
    });

    console.log("Authentication state reset successfully");
  } catch (error) {
    console.error("Error resetting auth state:", error);
    throw error;
  }
}
