/**
 * Centralized authentication error handling utilities
 * 
 * This module provides consistent error handling for Supabase authentication
 * across the application, with specific handling for refresh token issues.
 */

import { AuthError } from '@supabase/supabase-js';

export interface AuthErrorInfo {
  isRefreshTokenError: boolean;
  isNetworkError: boolean;
  shouldClearSession: boolean;
  shouldRetry: boolean;
  userMessage: string;
  logLevel: 'error' | 'warn' | 'info';
}

/**
 * Analyzes authentication errors and provides handling recommendations
 */
export function analyzeAuthError(error: AuthError | Error | unknown): AuthErrorInfo {
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorCode = (error as any)?.code || '';

  // Check for refresh token errors
  const isRefreshTokenError = 
    errorMessage.includes('refresh_token_not_found') ||
    errorMessage.includes('Invalid Refresh Token') ||
    errorMessage.includes('refresh_token_expired') ||
    errorCode === 'refresh_token_not_found';

  // Check for network errors
  const isNetworkError = 
    errorMessage.includes('fetch') ||
    errorMessage.includes('network') ||
    errorMessage.includes('timeout') ||
    errorCode === 'network_error';

  // Determine if session should be cleared
  const shouldClearSession = isRefreshTokenError || 
    errorMessage.includes('invalid_token') ||
    errorMessage.includes('jwt_expired');

  // Determine if operation should be retried
  const shouldRetry = isNetworkError && !isRefreshTokenError;

  // Determine appropriate log level
  let logLevel: 'error' | 'warn' | 'info' = 'error';
  if (isRefreshTokenError) {
    logLevel = 'warn'; // Refresh token errors are expected and handled gracefully
  } else if (isNetworkError) {
    logLevel = 'warn'; // Network errors are temporary
  }

  // Generate user-friendly message
  let userMessage = 'An authentication error occurred. Please try again.';
  if (isRefreshTokenError) {
    userMessage = 'Your session has expired. Please sign in again.';
  } else if (isNetworkError) {
    userMessage = 'Network error. Please check your connection and try again.';
  } else if (errorMessage.includes('Email not confirmed')) {
    userMessage = 'Please verify your email before signing in.';
  } else if (errorMessage.includes('Invalid login credentials')) {
    userMessage = 'Invalid email or password. Please try again.';
  }

  return {
    isRefreshTokenError,
    isNetworkError,
    shouldClearSession,
    shouldRetry,
    userMessage,
    logLevel,
  };
}

/**
 * Logs authentication errors with appropriate level and context
 */
export function logAuthError(
  error: AuthError | Error | unknown, 
  context: string,
  additionalInfo?: Record<string, any>
): void {
  const errorInfo = analyzeAuthError(error);
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  const logData = {
    context,
    error: errorMessage,
    errorInfo,
    ...additionalInfo,
  };

  switch (errorInfo.logLevel) {
    case 'error':
      console.error(`Auth Error [${context}]:`, logData);
      break;
    case 'warn':
      console.warn(`Auth Warning [${context}]:`, logData);
      break;
    case 'info':
      console.info(`Auth Info [${context}]:`, logData);
      break;
  }
}

/**
 * Handles authentication errors consistently across the application
 */
export function handleAuthError(
  error: AuthError | Error | unknown,
  context: string,
  options: {
    onClearSession?: () => void;
    onRetry?: () => void;
    onShowMessage?: (message: string) => void;
  } = {}
): AuthErrorInfo {
  const errorInfo = analyzeAuthError(error);
  
  // Log the error
  logAuthError(error, context);

  // Clear session if needed
  if (errorInfo.shouldClearSession && options.onClearSession) {
    console.log(`Clearing session due to auth error in ${context}`);
    options.onClearSession();
  }

  // Show user message if callback provided
  if (options.onShowMessage) {
    options.onShowMessage(errorInfo.userMessage);
  }

  // Trigger retry if appropriate and callback provided
  if (errorInfo.shouldRetry && options.onRetry) {
    console.log(`Retrying operation after auth error in ${context}`);
    setTimeout(options.onRetry, 1000); // Retry after 1 second
  }

  return errorInfo;
}

/**
 * Clears authentication-related cookies and storage
 */
export function clearAuthData(): void {
  try {
    // Clear localStorage items that might contain auth data
    const authKeys = [
      'supabase.auth.token',
      'sb-access-token',
      'sb-refresh-token',
    ];
    
    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear sessionStorage items
    authKeys.forEach(key => {
      sessionStorage.removeItem(key);
    });

    console.log('Cleared local auth data');
  } catch (error) {
    console.warn('Error clearing auth data:', error);
  }
}

/**
 * Checks if an error is a known authentication issue that can be handled gracefully
 */
export function isHandleableAuthError(error: AuthError | Error | unknown): boolean {
  const errorInfo = analyzeAuthError(error);
  return errorInfo.isRefreshTokenError || errorInfo.isNetworkError;
}

/**
 * Creates a retry function with exponential backoff for auth operations
 */
export function createAuthRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): () => Promise<T> {
  return async (): Promise<T> => {
    let lastError: unknown;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        const errorInfo = analyzeAuthError(error);
        
        // Don't retry if it's not a retryable error
        if (!errorInfo.shouldRetry) {
          throw error;
        }
        
        // Don't retry on the last attempt
        if (attempt === maxAttempts) {
          break;
        }
        
        // Wait before retrying with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`Auth operation failed, retrying in ${delay}ms (attempt ${attempt}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  };
}
