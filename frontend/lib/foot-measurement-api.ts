/**
 * Foot Measurement API Service
 * 
 * This service handles communication with the foot measurement backend
 * that analyzes foot images and returns length/width measurements.
 */

export interface FootMeasurementResult {
  image_index: number;
  filename: string;
  length_mm: number;
  width_mm: number;
  status: 'success' | 'error';
  error_message?: string;
}

export interface FootMeasurementResponse {
  status: 'completed' | 'error';
  processed_images: number;
  measurements: FootMeasurementResult[];
  error?: string;
}

export interface FootMeasurementRequest {
  topLeftImage?: File;
  topRightImage?: File;
}

/**
 * Calls the foot measurement API to analyze foot images
 * @param images Object containing the foot images to analyze
 * @returns Promise with measurement results
 */
export async function analyzeFootMeasurements(
  images: FootMeasurementRequest
): Promise<FootMeasurementResponse> {
  try {
    // Validate input
    if (!images.topLeftImage && !images.topRightImage) {
      throw new Error('At least one foot image is required for analysis');
    }

    // Create FormData for multipart/form-data request
    const formData = new FormData();
    
    if (images.topLeftImage) {
      formData.append('images', images.topLeftImage);
    }
    
    if (images.topRightImage) {
      formData.append('images', images.topRightImage);
    }

    // Call the measurement API
    const response = await fetch('http://*************:8000/measure', {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let the browser set it with boundary
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data: FootMeasurementResponse = await response.json();
    
    // Validate response structure
    if (!data.measurements || !Array.isArray(data.measurements)) {
      throw new Error('Invalid response format from measurement API');
    }

    return data;
  } catch (error) {
    console.error('Foot measurement API error:', error);
    
    // Return error response in expected format
    return {
      status: 'error',
      processed_images: 0,
      measurements: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Extracts measurements for a specific foot from the API response
 * @param measurements Array of measurement results
 * @param imageIndex Index of the image (0 for first, 1 for second)
 * @returns Measurement data or null if not found
 */
export function getMeasurementByIndex(
  measurements: FootMeasurementResult[],
  imageIndex: number
): FootMeasurementResult | null {
  return measurements.find(m => m.image_index === imageIndex) || null;
}

/**
 * Validates that measurement results are within reasonable ranges
 * @param measurement Measurement result to validate
 * @returns Object with validation result and any warnings
 */
export function validateMeasurement(measurement: FootMeasurementResult): {
  isValid: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  let isValid = true;

  // Check length (typical adult foot: 200-320mm)
  if (measurement.length_mm < 150 || measurement.length_mm > 400) {
    warnings.push(`Foot length ${measurement.length_mm}mm seems unusual (expected 200-320mm)`);
    if (measurement.length_mm < 100 || measurement.length_mm > 500) {
      isValid = false;
    }
  }

  // Check width (typical adult foot: 80-120mm)
  if (measurement.width_mm < 60 || measurement.width_mm > 150) {
    warnings.push(`Foot width ${measurement.width_mm}mm seems unusual (expected 80-120mm)`);
    if (measurement.width_mm < 40 || measurement.width_mm > 200) {
      isValid = false;
    }
  }

  return { isValid, warnings };
}

/**
 * Formats measurement values for display
 * @param value Measurement value in mm
 * @param unit Unit to display ('mm' or 'cm')
 * @returns Formatted string
 */
export function formatMeasurement(value: number, unit: 'mm' | 'cm' = 'mm'): string {
  if (unit === 'cm') {
    return `${(value / 10).toFixed(1)} cm`;
  }
  return `${value.toFixed(1)} mm`;
}

/**
 * Converts measurements to the format expected by the running profile
 * @param measurements Array of measurement results
 * @returns Object with left and right foot measurements
 */
export function convertToProfileMeasurements(measurements: FootMeasurementResult[]): {
  heelToToeLengthLeft?: number;
  heelToToeLengthRight?: number;
  forefootWidthLeft?: number;
  forefootWidthRight?: number;
} {
  const result: {
    heelToToeLengthLeft?: number;
    heelToToeLengthRight?: number;
    forefootWidthLeft?: number;
    forefootWidthRight?: number;
  } = {};

  // Assuming first image is left foot, second is right foot
  // This mapping should be adjusted based on the actual image order
  const leftFootMeasurement = getMeasurementByIndex(measurements, 0);
  const rightFootMeasurement = getMeasurementByIndex(measurements, 1);

  if (leftFootMeasurement && leftFootMeasurement.status === 'success') {
    result.heelToToeLengthLeft = leftFootMeasurement.length_mm;
    result.forefootWidthLeft = leftFootMeasurement.width_mm;
  }

  if (rightFootMeasurement && rightFootMeasurement.status === 'success') {
    result.heelToToeLengthRight = rightFootMeasurement.length_mm;
    result.forefootWidthRight = rightFootMeasurement.width_mm;
  }

  return result;
}
