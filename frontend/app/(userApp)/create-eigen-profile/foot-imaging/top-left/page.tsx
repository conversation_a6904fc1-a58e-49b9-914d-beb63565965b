"use client";

import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { FootMeasurementStep } from "@/components/create-running-profile/FootMeasurementStep";

export default function FootImageTopLeftPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex((step) =>
    step.href.includes("foot-imaging/top-left")
  );
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <FootMeasurementStep
      title="Foot Image: Top Left"
      fieldName="footImageTopLeftUrl"
      nextStepHref={nextStep?.href || "/"}
      backStepHref={prevStep?.href || "/"}
      currentStepIndex={currentStepIndex}
      isLeftFoot={true}
    />
  );
}
