"use client";

import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { FootMeasurementStep } from "@/components/create-running-profile/FootMeasurementStep";

export default function FootImageTopRightPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex((step) =>
    step.href.includes("foot-imaging/top-right")
  );
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <FootMeasurementStep
      title="Foot Image: Top Right"
      fieldName="footImageTopRightUrl"
      nextStepHref={nextStep?.href || "/"}
      backStepHref={prevStep?.href || "/"}
      currentStepIndex={currentStepIndex}
      isLeftFoot={false}
    />
  );
}
