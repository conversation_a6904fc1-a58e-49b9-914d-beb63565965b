import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";

// Foot measurement service configuration
const MEASUREMENT_API_URL = process.env.FOOT_MEASUREMENT_API_URL || "http://63.177.99.227:8000/measure";
const API_TIMEOUT = 30000; // 30 seconds timeout

export interface FootMeasurementResult {
  image_index: number;
  filename: string;
  length_mm: number;
  width_mm: number;
  status: 'success' | 'error';
  error_message?: string;
}

export interface FootMeasurementResponse {
  status: 'completed' | 'error';
  processed_images: number;
  measurements: FootMeasurementResult[];
  error?: string;
}

/**
 * POST /api/foot-measurement
 * Proxies foot measurement requests to the external measurement service
 */
export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log(`Foot measurement request from user: ${user.id}`);

    // Get the form data from the request
    const formData = await request.formData();
    
    // Validate that we have at least one image
    const images = formData.getAll('images') as File[];
    if (images.length === 0) {
      return NextResponse.json(
        { 
          status: 'error',
          error: 'No images provided for analysis',
          processed_images: 0,
          measurements: []
        } as FootMeasurementResponse,
        { status: 400 }
      );
    }

    console.log(`Processing ${images.length} images for foot measurement`);

    // Validate image files
    for (const image of images) {
      if (!image || !(image instanceof File)) {
        return NextResponse.json(
          {
            status: 'error',
            error: 'Invalid image file provided',
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 400 }
        );
      }

      // Check file size (limit to 10MB per image)
      if (image.size > 10 * 1024 * 1024) {
        return NextResponse.json(
          {
            status: 'error',
            error: `Image file too large: ${image.name}. Maximum size is 10MB.`,
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 400 }
        );
      }

      // Check file type
      if (!image.type.startsWith('image/')) {
        return NextResponse.json(
          {
            status: 'error',
            error: `Invalid file type: ${image.name}. Only image files are allowed.`,
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 400 }
        );
      }
    }

    // Create a new FormData for the external API
    const apiFormData = new FormData();
    for (const image of images) {
      apiFormData.append('images', image);
    }

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    try {
      console.log(`Calling measurement API: ${MEASUREMENT_API_URL}`);
      
      // Make request to the measurement service
      const response = await fetch(MEASUREMENT_API_URL, {
        method: 'POST',
        body: apiFormData,
        signal: controller.signal,
        // Don't set Content-Type header - let fetch set it with boundary for multipart
      });

      clearTimeout(timeoutId);

      console.log(`Measurement API response status: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Measurement API error: ${response.status} - ${errorText}`);
        
        return NextResponse.json(
          {
            status: 'error',
            error: `Measurement service error: ${response.status} ${response.statusText}`,
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 502 } // Bad Gateway
        );
      }

      // Parse the response
      const measurementData: FootMeasurementResponse = await response.json();
      
      console.log(`Measurement completed: ${measurementData.processed_images} images processed`);

      // Validate response structure
      if (!measurementData.measurements || !Array.isArray(measurementData.measurements)) {
        console.error('Invalid response structure from measurement API');
        return NextResponse.json(
          {
            status: 'error',
            error: 'Invalid response from measurement service',
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 502 }
        );
      }

      // Log measurement results for debugging
      measurementData.measurements.forEach((measurement, index) => {
        if (measurement.status === 'success') {
          console.log(`Image ${index}: ${measurement.length_mm}mm x ${measurement.width_mm}mm`);
        } else {
          console.warn(`Image ${index} failed: ${measurement.error_message}`);
        }
      });

      // Return the measurement results
      return NextResponse.json(measurementData);

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('Measurement API request timed out');
        return NextResponse.json(
          {
            status: 'error',
            error: 'Measurement service request timed out. Please try again.',
            processed_images: 0,
            measurements: []
          } as FootMeasurementResponse,
          { status: 504 } // Gateway Timeout
        );
      }

      console.error('Error calling measurement API:', fetchError);
      return NextResponse.json(
        {
          status: 'error',
          error: 'Failed to connect to measurement service. Please try again later.',
          processed_images: 0,
          measurements: []
        } as FootMeasurementResponse,
        { status: 502 }
      );
    }

  } catch (error) {
    console.error('Unexpected error in foot measurement API:', error);
    return NextResponse.json(
      {
        status: 'error',
        error: 'An unexpected error occurred during measurement analysis.',
        processed_images: 0,
        measurements: []
      } as FootMeasurementResponse,
      { status: 500 }
    );
  }
}

/**
 * GET /api/foot-measurement
 * Returns API status and configuration info
 */
export async function GET() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    return NextResponse.json({
      status: 'available',
      endpoint: '/api/foot-measurement',
      methods: ['POST'],
      description: 'Foot measurement analysis service',
      maxFileSize: '10MB',
      supportedFormats: ['image/jpeg', 'image/png', 'image/webp'],
      timeout: `${API_TIMEOUT / 1000}s`,
    });
  } catch (error) {
    console.error('Error in foot measurement API status:', error);
    return NextResponse.json(
      { error: 'Service unavailable' },
      { status: 500 }
    );
  }
}
