"use client";

import React, { useState, useEffect, useTransition } from "react";
import Image from "next/image";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  Trash2,
  Loader2,
  ArrowLeft,
  ArrowRight,
  Camera,
  Calculator,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { uploadFile } from "@/lib/upload-helpers";
import { toast } from "sonner";
import { motion } from "framer-motion";
import {
  analyzeFootMeasurements,
  validateMeasurement,
  formatMeasurement,
  convertToProfileMeasurements,
  type FootMeasurementResult,
} from "@/lib/foot-measurement-api";

interface FootMeasurementStepProps {
  title: string;
  fieldName: "footImageTopLeftUrl" | "footImageTopRightUrl";
  nextStepHref: string;
  backStepHref: string;
  currentStepIndex: number;
  isLeftFoot: boolean;
}

// Content guidance for foot imaging
const getGuidanceContent = (isLeftFoot: boolean) => {
  const footSide = isLeftFoot ? "left" : "right";
  return `Place your ${footSide} foot flat on a white sheet of paper. Take a photo from directly above, ensuring your entire foot is visible with clear toe and heel definition. Use good lighting and avoid shadows for optimal analysis.`;
};

export const FootMeasurementStep: React.FC<FootMeasurementStepProps> = ({
  title,
  fieldName,
  nextStepHref,
  backStepHref,
  currentStepIndex,
  isLeftFoot,
}) => {
  const { profileId, profileData, updateProfileData, setCurrentStep } = useRunningProfile();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [measurements, setMeasurements] = useState<FootMeasurementResult | null>(null);
  const [hasAnalyzed, setHasAnalyzed] = useState(false);

  useEffect(() => {
    const existingUrl = profileData[fieldName] as string | null;
    if (existingUrl) {
      setPreviewUrl(existingUrl);
    }
  }, [profileData, fieldName]);

  useEffect(() => {
    if (file) {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else if (!profileData[fieldName]) {
      setPreviewUrl(null);
    }
  }, [file, profileData, fieldName]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      if (!selectedFile.type.startsWith("image/")) {
        setError("Please select an image file.");
        return;
      }

      setFile(selectedFile);
      setError(null);
      setMeasurements(null);
      setHasAnalyzed(false);
    }
  };

  const handleRemove = () => {
    setFile(null);
    setError(null);
    setPreviewUrl(profileData[fieldName] as string | null);
    setMeasurements(null);
    setHasAnalyzed(false);
  };

  const handleAnalyze = async () => {
    if (!file) {
      setError("Please select an image first.");
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // For analysis, we need both feet images if available
      const otherFieldName = isLeftFoot ? "footImageTopRightUrl" : "footImageTopLeftUrl";
      const otherImageUrl = profileData[otherFieldName] as string | null;
      
      // Create request with current image and any existing other foot image
      const request = isLeftFoot 
        ? { topLeftImage: file }
        : { topRightImage: file };

      const result = await analyzeFootMeasurements(request);

      if (result.status === 'error') {
        throw new Error(result.error || 'Analysis failed');
      }

      // Get measurement for current image (assuming index 0 for single image)
      const measurement = result.measurements[0];
      if (!measurement || measurement.status !== 'success') {
        throw new Error(measurement?.error_message || 'No valid measurement found');
      }

      // Validate measurement
      const validation = validateMeasurement(measurement);
      if (!validation.isValid) {
        setError(`Measurement validation failed: ${validation.warnings.join(', ')}`);
        return;
      }

      if (validation.warnings.length > 0) {
        toast.warning(`Analysis completed with warnings: ${validation.warnings.join(', ')}`);
      }

      setMeasurements(measurement);
      setHasAnalyzed(true);
      toast.success("Foot measurement analysis completed!");

      // Store measurements in profile data
      const profileMeasurements = convertToProfileMeasurements([measurement]);
      await updateProfileData(profileMeasurements);

    } catch (err) {
      const message = err instanceof Error ? err.message : "Analysis failed";
      setError(message);
      toast.error(message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleNext = () => {
    if (!file) {
      setError("Please select an image to continue.");
      return;
    }
    if (!profileId) {
      setError("Profile not found. Please try again.");
      return;
    }

    setError(null);
    setIsUploading(true);

    startTransition(async () => {
      try {
        const result = await uploadFile(file, profileId, fieldName);

        if (result.error) {
          throw new Error(result.error);
        }

        if (result.fileUrl) {
          await updateProfileData({ [fieldName]: result.fileUrl });
          toast.success("Image uploaded successfully!");
          setCurrentStep(currentStepIndex + 1);
          router.push(nextStepHref);
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : "Upload failed";
        setError(message);
        toast.error(message);
      } finally {
        setIsUploading(false);
      }
    });
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(backStepHref);
  };

  const isLoading = isUploading || isPending || isAnalyzing;
  const guidanceText = getGuidanceContent(isLeftFoot);

  return (
    <div className="flex flex-col max-w-4xl mx-auto px-4 py-2">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col"
      >
        {/* Guidance Text */}
        <div className="text-center mb-2">
          <p className="text-sm text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            {guidanceText}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-500/50 text-sm rounded-lg text-center mb-2">
            {error}
          </div>
        )}

        {/* Content Area - Two Column Layout on Desktop */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          {/* Upload Controls */}
          <div className="flex flex-col justify-center space-y-2">
            <Input
              id="imageUpload"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
              disabled={isLoading}
            />

            <Label
              htmlFor="imageUpload"
              className={`flex items-center justify-center gap-2 cursor-pointer rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/30 px-3 py-2.5 text-sm font-medium text-muted-foreground transition-colors hover:bg-muted/50 hover:border-muted-foreground/50 ${
                isLoading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              <Camera className="h-4 w-4" />
              <span className="text-sm">
                {file ? "Change image" : "Select image"}
              </span>
            </Label>

            {file && (
              <>
                <Button
                  variant="outline"
                  onClick={handleRemove}
                  disabled={isLoading}
                  className="flex items-center justify-center gap-2 text-destructive hover:text-destructive border-destructive/50 hover:border-destructive hover:bg-destructive/5 h-8"
                  size="sm"
                >
                  <Trash2 className="h-3 w-3" />
                  <span className="text-xs">Remove</span>
                </Button>

                <Button
                  onClick={handleAnalyze}
                  disabled={isLoading || hasAnalyzed}
                  className="flex items-center justify-center gap-2 h-8"
                  size="sm"
                  variant={hasAnalyzed ? "secondary" : "default"}
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin" />
                      <span className="text-xs">Analyzing...</span>
                    </>
                  ) : hasAnalyzed ? (
                    <>
                      <CheckCircle className="h-3 w-3" />
                      <span className="text-xs">Analyzed</span>
                    </>
                  ) : (
                    <>
                      <Calculator className="h-3 w-3" />
                      <span className="text-xs">Analyze Image</span>
                    </>
                  )}
                </Button>
              </>
            )}
          </div>

          {/* Preview Area */}
          <div className="border border-border rounded-lg p-2 bg-muted/20 flex items-center justify-center h-[160px] lg:h-[200px]">
            {previewUrl ? (
              <div className="relative w-full h-full max-h-[140px] lg:max-h-[180px]">
                <Image
                  src={previewUrl}
                  alt="Preview"
                  fill
                  className="object-contain rounded-lg"
                  priority
                />
              </div>
            ) : (
              <div className="flex flex-col items-center text-center">
                <Camera className="h-8 w-8 text-muted-foreground/40 mb-2" />
                <p className="text-xs text-muted-foreground">
                  Preview will appear here
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Measurement Results */}
        {measurements && (
          <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-2">
              Measurement Results
            </h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Length:</span>
                <span className="ml-1 font-medium">{formatMeasurement(measurements.length_mm)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Width:</span>
                <span className="ml-1 font-medium">{formatMeasurement(measurements.width_mm)}</span>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-center pt-2 pb-4 gap-2">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={isLoading}
          className="flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <Button
          onClick={handleNext}
          disabled={!file || isLoading}
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              Next
              <ArrowRight className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};
